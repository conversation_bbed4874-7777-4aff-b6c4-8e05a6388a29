# 技术方案文档 (TDD) - 智能书签管家 (Bookmark Tidy)
**版本: 1.0**
**日期: 2025-07-22**

---

## 2. 系统架构

### 2.1. 总体架构
本系统采用基于 **Vercel** 和 **Supabase** 的现代 **Serverless** 架构。此架构旨在最大化开发效率和最小化运维成本，前端通过Next.js构建并托管于Vercel，后端逻辑也由Next.js的API Routes承载，后端所需的数据、认证和存储服务则由Supabase提供。这种架构下，开发者无需管理传统服务器，系统可根据请求量自动扩缩容。

### 2.2. 架构图

*(注：此为Mermaid.js代码，可在支持的Markdown查看器中渲染为流程图，如VS Code搭配Mermaid插件或Typora。)*
```mermaid
graph TD
    subgraph "User's Browser"
        UserDevice[Client: React/Next.js UI]
    end

    subgraph "Vercel Platform (Serverless)"
        APIRoutes[Backend Logic: Next.js API Routes]
    end

    subgraph "Supabase BaaS (Backend as a Service)"
        Auth[Supabase Auth]
        DB[Supabase DB (PostgreSQL)]
        Storage[Supabase Storage]
    end
    
    subgraph "Third-Party AI Service"
        AI[OpenAI API (GPT-4/GPT-3.5)]
    end

    UserDevice -- HTTPS Requests --> APIRoutes;
    UserDevice -- Auth redirects/JWT --> Auth;
    APIRoutes -- SQL Queries (via Supabase SDK) --> DB;
    APIRoutes -- File Operations --> Storage;
    APIRoutes -- User Auth Checks --> Auth;
    APIRoutes -- AI Prompts (Server-side) --> AI;
```

### 2.3. 技术选型

| 领域 | 技术栈 | 选型理由 |
| :--- | :--- | :--- |
| **全栈框架** | **Next.js 15 (App Router)** | 集成前后端，简化开发流程；对Serverless有原生支持，与Vercel部署完美契合；庞大的React生态和活跃社区。 |
| **后端服务 (BaaS)** | **Supabase** | 提供PostgreSQL数据库、认证、存储、边缘函数等一站式服务。开源且基于标准技术（如Postgres），无厂商锁定风险。优秀的免费套餐，足够支撑项目启动。 |
| **UI库** | **Tailwind CSS + Shadcn/ui** | Tailwind CSS提供高效的原子化样式编写方式，灵活性高。Shadcn/ui提供高质量、可访问的组件，通过复制代码而非依赖包的方式引入，易于定制和维护。 |
| **HTML解析库** | **Cheerio.js** | 在Node.js环境中运行，性能优秀，其API设计与jQuery类似，对于前端开发者学习成本低，非常适合解析和遍历静态HTML文档结构。 |
| **AI模型服务** | **OpenAI API (GPT-4 / 3.5-Turbo)** | 具备强大的上下文理解和指令遵循能力，是完成书签分类和自然语言指令解析的理想选择。API成熟稳定，有详细的文档和丰富的社区支持。 |
|**登录注册**|**NextAuth5**| 和nextjs框架兼容，并且高度可定制且免费。|

## 3. 数据库设计

*数据库系统: Supabase PostgreSQL*

### 3.1. ERD (实体关系图)

```mermaid
erDiagram
    users {
        uuid id PK "auth.users.id"
    }

    user_sessions {
        uuid id PK
        uuid user_id FK "关联到用户"
    }

    categories {
        uuid id PK
        uuid user_id FK "关联到用户"
        string name
    }

    bookmarks {
        uuid id PK
        uuid user_id FK "关联到用户"
        uuid session_id FK "关联到会话"
        uuid category_id FK "关联到分类"
        string title
        text url
    }

    users ||--o{ user_sessions : "发起"
    users ||--o{ categories : "拥有"
    users ||--o{ bookmarks : "拥有"
    
    user_sessions ||--o{ bookmarks : "包含"
    
    categories ||--o{ bookmarks : "归类于"
```

### 3.2. 表结构详解

*数据库系统: Supabase PostgreSQL*

#### `profiles` (与`auth.users`一对一关联，存储公开信息)
-   `id` (`uuid`, PK, `references auth.users.id`)
-   `email` (`varchar`, not null)
-   `created_at` (`timestamptz`, `default: now()`)

#### `user_sessions` (记录每一次导入整理的会话)
-   `id` (`uuid`, PK, `default: gen_random_uuid()`)
-   `user_id` (`uuid`, FK -> `profiles.id`, not null)
-   `status` (`varchar`, `default: 'processing'`) - 可选值: `'processing'`, `'done'`, `'error'`
-   `few_shot_examples` (`jsonb`, nullable) - **核心字段**，用于存储本次会話中用户修正的分类示例，格式：`[{"title": "...", "url": "...", "category": "..."}, ...]`
-   `created_at` (`timestamptz`, `default: now()`)

#### `categories` (用户创建的分类)
-   `id` (`uuid`, PK, `default: gen_random_uuid()`)
-   `user_id` (`uuid`, FK -> `profiles.id`, not null)
-   `name` (`varchar`, not null)
-   `created_at` (`timestamptz`, `default: now()`)
-   **Constraint**: `UNIQUE(user_id, name)`，此约束确保同一用户不能创建同名的分类。

#### `bookmarks` (解析出的书签条目)
-   `id` (`uuid`, PK, `default: gen_random_uuid()`)
-   `user_id` (`uuid`, FK -> `profiles.id`, not null)
-   `session_id` (`uuid`, FK -> `user_sessions.id`, not null)
-   `category_id` (`uuid`, FK -> `categories.id`, nullable)
-   `title` (`text`, not null)
-   `url` (`text`, not null)
-   `original_path` (`text`, nullable)
-   `status` (`varchar`, `default: 'unconfirmed'`) - 可选值: `'unconfirmed'`, `'confirmed'`, `'dead_link'`
-   `ai_suggestion` (`jsonb`, nullable) - 存储AI的建议，格式: `{ "category": "Technology", "confidence": 0.95 }`
-   `created_at` (`timestamptz`, `default: now()`)

## 4. API 端点设计 (MVP)

API遵循RESTful风格，以资源为中心进行设计。所有API端点都需要用户认证（通过JWT）。

| 端点路径 | HTTP方法 | 描述 | 请求体 (Body) | 成功响应 (200 OK) |
| :--- | :--- | :--- | :--- | :--- |
| `/api/sessions` | `POST` | 用户上传HTML文件，创建一个新的整理会话，并启动后台解析和AI分类任务。 | `FormData` (包含`.html`文件) | `{ "sessionId": "uuid-of-the-new-session" }` |
| `/api/sessions/:id`| `GET` | 获取指定ID的整理会话的完整数据，包括其下所有书签和相关分类。 | (无) | `{ "session": {...}, "bookmarks": [...], "categories": [...] }` |
| `/api/bookmarks/batch`| `PATCH` | 批量更新书签。主要用于将多个书签移动到同一个新分类中。 | `{ "bookmarkIds": ["uuid1", ...], "categoryId": "uuid" }` | `{ "success": true, "count": 5 }` |
| `/api/bookmarks/:id` | `PATCH` | 更新单个书签的信息。主要用于用户手动修正单个书签的分类。 | `{ "categoryId": "new-category-uuid" }` | `{ "updatedBookmark": {...} }` |
| `/api/categories` | `POST` | 在当前整理会话关联的用户下，创建一个新的分类。 | `{ "name": "New Category Name" }` | `{ "newCategory": {...} }` |
| `/api/categories/:id`| `PATCH` | 更新指定ID的分类的名称。 | `{ "name": "Updated Name" }` | `{ "updatedCategory": {...} }` |
| `/api/export/:id` | `GET` | 根据会话ID生成并提供整理好的HTML格式书签文件供用户下载。 | (无) | 文件流 (`Content-Type: text/html`) |

## 5. 核心算法与逻辑

### 5.1. 书签HTML递归解析逻辑 (后端)

1.  **文件接收**: 在 Next.js 的 API Route 中接收客户端上传的 `FormData`。
2.  **加载到Cheerio**: 将文件缓冲区(Buffer)转换为字符串，并加载到 Cheerio 实例中，以便进行DOM操作：`const $ = cheerio.load(fileBuffer.toString());`
3.  **递归解析**: `Netscape-Bookmark-file-1` 格式本质上是基于`<DL><p>`标签包裹的嵌套定义列表。处理这种结构最有效的方法是编写一个递归函数。

    ```javascript
    // 后端API Route中的伪代码示例
    function parseBookmarks(node, currentPath = []) {
        const bookmarks = [];
        // 直接子节点中的 <DT> 标签是书签或文件夹的容器
        node.children('dt').each((index, dtNode) => {
            const dt = $(dtNode);
            const link = dt.children('a').first(); // 获取第一个<a>标签，即书签链接
            const folder = dt.children('h3').first(); // 获取第一个<h3>标签，即文件夹名称

            if (link.length > 0) { // 如果找到了<a>标签，说明这是一个书签
                bookmarks.push({
                    title: link.text(),
                    url: link.attr('href'),
                    original_path: currentPath.join(' / ') // 用 ' / ' 作为路径分隔符
                });
            } else if (folder.length > 0) { // 如果找到了<h3>标签，说明这是一个文件夹
                const folderName = folder.text();
                const subDl = dt.next('dl').first(); // 文件夹的内容定义在紧随其后的<DL>标签中
                if (subDl.length > 0) {
                    // 递归进入下一层，并将当前文件夹名称加入路径
                    const newPath = [...currentPath, folderName];
                    bookmarks.push(...parseBookmarks(subDl, newPath));
                }
            }
        });
        return bookmarks;
    }

    // 从根部的<DL>开始解析
    const rootDl = $('body > dl').first();
    const allBookmarks = parseBookmarks(rootDl, []);
    // 接下来，将 allBookmarks 存入数据库
    ```

### 5.2. AI 分类与“会话级学习”实现

*   **核心机制**: 通过在每次API请求中动态构建带有上下文示例的提示（Few-Shot Prompting）来实现。
*   **Prompt 模板**:

    ```text
    # Role: Bookmark Classification Expert

    # Task
    You are an expert at classifying browser bookmarks. Your goal is to assign the most accurate category to the given bookmark based on its title and URL. You must only use one of the provided categories.

    # Available Categories
    {{available_categories}}

    # Examples (Learn from these user corrections during this session)
    {{#each few_shot_examples}}
    - User Bookmark: "{{this.title}}" ({{this.url}}) -> Correct Category: {{this.category}}
    {{/each}}
    
    # New Bookmark to Classify
    - Title: "{{bookmark.title}}"
    - URL: "{{bookmark.url}}"

    # Output Format
    Return ONLY a JSON object with two keys: "category" and "confidence" (a float between 0 and 1 indicating your certainty). Do not add any other text, explanation or markdown formatting.
    ```
*   **后端处理流程**:
    1.  当一个整理会话开始时，后台对所有解析出的书签发起AI分类任务。
    2.  对于每个书签，从`user_sessions`表中获取当前会话的`few_shot_examples`数组。
    3.  从`categories`表中获取用户定义的所有`available_categories`。
    4.  使用上述数据动态构建完整的Prompt。
    5.  调用OpenAI API，并将返回的JSON存入对应`bookmarks`条目的`ai_suggestion`字段。
    6.  **实现学习**: 当用户在前端修正了一个书签的分类（触发 `PATCH /api/bookmarks/:id`）时，后端API除了更新`bookmarks`表，还会将这个修正案例 `{"title": "...", "url": "...", "category": "..."}` **追加**到当前 `user_sessions` 的 `few_shot_examples` JSONB数组中。这样，下一次为该会话的书签分类时，AI就会参考这个新的、准确的示例。


## 6. 部署与运维 (DevOps)

- **部署 (Deployment)**:
  -   项目主分支 (`main` or `master`) 与Vercel项目直接关联。任何 `git push`到该分支的操作都将自动触发Vercel的构建和生产环境部署流程。

- **环境管理 (Environment Management)**:
  -   **生产环境**: `main`分支。
  -   **预览环境**: 任何从`main`分支创建的Pull/Merge Request都会自动生成一个独立的预览URL，方便在合并前进行测试。
  -   **环境变量**: 所有敏感密钥（如Supabase的URL和Anon/Service Key，OpenAI的API Key）都将作为环境变量存储在Vercel的项目设置中，绝不硬编码在代码里。

- **持续集成/持续部署 (CI/CD)**:
  -   Vercel平台提供开箱即用的CI/CD。无需复杂的`.yml`配置文件。构建、测试（可选）、部署流程全自动化。

- **监控 (Monitoring)**:
  -   **前端性能**: 使用 **Vercel Analytics** 进行网站访问量、用户来源和Web Vitals（性能指标）的监控。
  -   **后端与数据库**: 使用 **Supabase** 自带的仪表盘监控数据库性能、API请求量和查询日志。

- **后台任务 (Background Tasks)**:
  -   对于未来需要执行定时任务的功能（如每日扫描死链），将使用 **Vercel Cron Jobs**。可配置一个标准的cron表达式，使其定期调用一个特定的API Route来执行扫描逻辑。