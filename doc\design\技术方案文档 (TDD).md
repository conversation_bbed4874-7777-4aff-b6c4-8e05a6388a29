# 技术方案文档 (TDD) - 智能书签管家 (Bookmark Tidy)
**版本: 1.0**
**日期: 2025-07-22**

---

## 2. 系统架构

### 2.1. 总体架构
本系统采用基于 **Vercel** 和 **Supabase** 的现代 **Serverless** 架构。此架构旨在最大化开发效率和最小化运维成本，前端通过Next.js构建并托管于Vercel，后端逻辑也由Next.js的API Routes承载，后端所需的数据、认证和存储服务则由Supabase提供。这种架构下，开发者无需管理传统服务器，系统可根据请求量自动扩缩容。

### 2.2. 架构图 - 优化版本

*(注：此为Mermaid.js代码，可在支持的Markdown查看器中渲染为流程图，如VS Code搭配Mermaid插件或Typora。)*
```mermaid
graph TD
    subgraph "User's Browser"
        UserDevice[Client: React/Next.js UI]
    end

    subgraph "Vercel Platform (Serverless)"
        APIRoutes[Backend Logic: Next.js API Routes]
    end

    subgraph "Supabase BaaS (Backend as a Service)"
        Auth[Supabase Auth]
        DB[Supabase DB (PostgreSQL)]
    end

    subgraph "Cloudflare R2 (Object Storage)"
        R2[File Storage: HTML Bookmarks]
    end

    subgraph "Third-Party AI Service"
        AI[OpenAI API (GPT-4/GPT-3.5)]
    end

    UserDevice -- HTTPS Requests --> APIRoutes;
    UserDevice -- Auth redirects/JWT --> Auth;
    APIRoutes -- SQL Queries (Metadata Only) --> DB;
    APIRoutes -- File Upload/Download --> R2;
    APIRoutes -- User Auth Checks --> Auth;
    APIRoutes -- AI Prompts (Server-side) --> AI;
    APIRoutes -- Read Files for Processing --> R2;
```

### 2.3. 技术选型 - 优化版本

| 领域 | 技术栈 | 选型理由 |
| :--- | :--- | :--- |
| **全栈框架** | **Next.js 15 (App Router)** | 集成前后端，简化开发流程；对Serverless有原生支持，与Vercel部署完美契合；庞大的React生态和活跃社区。 |
| **后端服务 (BaaS)** | **Supabase** | 提供PostgreSQL数据库、认证等服务。开源且基于标准技术（如Postgres），无厂商锁定风险。优秀的免费套餐，足够支撑项目启动。 |
| **对象存储** | **Cloudflare R2** | **新增**：S3兼容的对象存储，成本极低（$0.015/GB/月），无出站流量费用。与Cloudflare CDN集成，全球访问速度快。完美替代数据库存储大文件。 |
| **UI库** | **Tailwind CSS + Shadcn/ui** | Tailwind CSS提供高效的原子化样式编写方式，灵活性高。Shadcn/ui提供高质量、可访问的组件，通过复制代码而非依赖包的方式引入，易于定制和维护。 |
| **HTML解析库** | **Cheerio.js** | 在Node.js环境中运行，性能优秀，其API设计与jQuery类似，对于前端开发者学习成本低，非常适合解析和遍历静态HTML文档结构。 |
| **AI模型服务** | **OpenAI API (GPT-4 / 3.5-Turbo)** | 具备强大的上下文理解和指令遵循能力，是完成书签分类和自然语言指令解析的理想选择。API成熟稳定，有详细的文档和丰富的社区支持。 |
| **认证系统** | **NextAuth5** | 和Next.js框架兼容，并且高度可定制且免费。与Supabase Auth集成良好。 |

## 3. 数据库设计

*数据库系统: Supabase PostgreSQL*

### 3.1. ERD (实体关系图) - 优化版本

```mermaid
erDiagram
    profiles {
        uuid id PK "auth.users.id"
        varchar email
        varchar display_name
        timestamptz created_at
        timestamptz updated_at
    }

    user_bookmark_workspace {
        uuid id PK
        uuid user_id FK "关联到用户"
        varchar status "processing/completed/failed"
        varchar original_file_key "原始文件R2键"
        varchar result_file_key "结果文件R2键"
        varchar original_filename "原始文件名"
        varchar browser_type "Chrome/Firefox/Edge等"
        integer total_bookmarks "总书签数"
        integer processed_bookmarks "已处理数"
        jsonb ai_learning_examples "AI学习示例"
        jsonb processing_metadata "处理元数据"
        timestamptz last_import_at "最后导入时间"
        timestamptz created_at
        timestamptz updated_at
    }

    user_categories {
        uuid id PK
        uuid user_id FK "关联到用户"
        varchar name "分类名称"
        varchar type "manual/ai/alphabetical"
        varchar color "分类颜色标识"
        text description "分类描述"
        integer sort_order "排序顺序"
        boolean is_default "是否默认分类"
        timestamptz created_at
        timestamptz updated_at
    }

    bookmark_processing_cache {
        uuid id PK
        uuid workspace_id FK "关联到工作空间"
        text title "书签标题"
        text url "书签URL"
        text original_path "原始文件夹路径"
        varchar status "unprocessed/confirmed/rejected"
        jsonb ai_suggestion "AI分类建议"
        varchar suggested_category "建议分类"
        varchar final_category "最终分类"
        varchar classification_type "manual/ai/alphabetical"
        boolean is_duplicate "是否重复"
        text duplicate_url "重复的URL"
        timestamptz created_at
    }

    profiles ||--|| user_bookmark_workspace : "拥有工作空间"
    profiles ||--o{ user_categories : "拥有分类"
    user_bookmark_workspace ||--o{ bookmark_processing_cache : "处理缓存"
```

### 3.2. 表结构详解 - 优化版本

*数据库系统: Supabase PostgreSQL + Cloudflare R2 对象存储*

#### `profiles` (用户基本信息表 - 与Supabase Auth集成)

**作用**: 存储用户的基本信息，与Supabase的auth.users表一对一关联，扩展用户属性。

**重要说明**: 密码由Supabase Auth系统管理，存储在`auth.users`表中，此表不存储密码。

- `id` (`uuid`, PK, `references auth.users.id`) - 用户唯一标识，关联到Supabase Auth
- `email` (`varchar`, not null) - 用户邮箱地址
- `display_name` (`varchar`, nullable) - 用户显示名称
- `created_at` (`timestamptz`, `default: now()`) - 账户创建时间
- `updated_at` (`timestamptz`, `default: now()`) - 最后更新时间

#### `user_bookmark_workspace` (用户书签工作空间表)

**作用**: 每个用户只有一个工作空间，存储当前和备份的书签文件信息。**核心优化：书签数据存储在Cloudflare R2，数据库只存元数据**。

**存储策略** (最简化方案):
- 原始文件: 用户上传的原始书签文件 (original.html)
- 结果文件: 整理完成的书签文件 (result.html，用户下载)
- 撤销机制: 由前端控制，不在服务端存储历史版本

**约束**: 每个用户只能有一个工作空间记录 (`UNIQUE(user_id)`)

- `id` (`uuid`, PK, `default: gen_random_uuid()`) - 工作空间唯一标识
- `user_id` (`uuid`, FK -> `profiles.id`, not null, UNIQUE) - 关联用户(一对一)
- `status` (`varchar`, `default: 'idle'`) - 工作空间状态: `'idle'`, `'processing'`, `'completed'`, `'failed'`
- `original_file_key` (`varchar`, nullable) - 原始书签文件在R2中的键名 (original.html)
- `result_file_key` (`varchar`, nullable) - 结果书签文件在R2中的键名 (result.html，用户下载)
- `original_filename` (`varchar`, nullable) - 原始导入文件名
- `browser_type` (`varchar`, nullable) - 浏览器类型: `'Chrome'`, `'Firefox'`, `'Edge'`, `'Safari'`
- `total_bookmarks` (`integer`, `default: 0`) - 总书签数量
- `processed_bookmarks` (`integer`, `default: 0`) - 已处理书签数量
- `ai_learning_examples` (`jsonb`, nullable) - **AI学习核心字段**，存储用户分类修正示例
- `processing_metadata` (`jsonb`, nullable) - 处理元数据，如文件大小、处理时间等
- `last_import_at` (`timestamptz`, nullable) - 最后导入时间
- `created_at` (`timestamptz`, `default: now()`) - 创建时间
- `updated_at` (`timestamptz`, `default: now()`) - 最后更新时间

#### `user_categories` (用户分类表)

**作用**: 存储用户创建的所有分类模板，支持手动创建、AI生成和字母分类等多种类型。**优化：与工作空间解耦，作为用户的全局分类模板**。

- `id` (`uuid`, PK, `default: gen_random_uuid()`) - 分类唯一标识
- `user_id` (`uuid`, FK -> `profiles.id`, not null) - 关联用户
- `name` (`varchar`, not null) - 分类名称
- `type` (`varchar`, `default: 'manual'`) - 分类类型: `'manual'`, `'ai'`, `'alphabetical'`
- `color` (`varchar`, nullable) - 分类颜色标识(十六进制色值)
- `description` (`text`, nullable) - 分类描述
- `sort_order` (`integer`, `default: 0`) - 显示排序顺序
- `is_default` (`boolean`, `default: false`) - 是否为默认分类(如"未分类")
- `created_at` (`timestamptz`, `default: now()`) - 创建时间
- `updated_at` (`timestamptz`, `default: now()`) - 最后更新时间
- **Constraint**: `UNIQUE(user_id, name)` - 确保同一用户不能创建同名分类

#### `bookmark_processing_cache` (书签处理缓存表)

**作用**: 临时存储正在处理的书签数据和分类结果。**优化：仅在处理期间使用，处理完成后清空，避免长期存储大量书签数据**。

**生命周期**:
1. 用户上传文件 → 解析书签 → 写入缓存表
2. AI分类 → 更新缓存表的分类建议
3. 用户确认分类 → 更新缓存表的最终分类
4. 生成最终文件 → 清空缓存表

- `id` (`uuid`, PK, `default: gen_random_uuid()`) - 缓存记录唯一标识
- `workspace_id` (`uuid`, FK -> `user_bookmark_workspace.id`, not null) - 关联工作空间
- `title` (`text`, not null) - 书签标题
- `url` (`text`, not null) - 书签URL地址
- `original_path` (`text`, nullable) - 原始文件夹路径(从HTML文件解析)
- `status` (`varchar`, `default: 'unprocessed'`) - 处理状态: `'unprocessed'`, `'confirmed'`, `'rejected'`
- `ai_suggestion` (`jsonb`, nullable) - AI分类建议: `{"category": "Technology", "confidence": 0.95}`
- `suggested_category` (`varchar`, nullable) - AI建议的分类名称
- `final_category` (`varchar`, nullable) - 用户确认的最终分类名称
- `classification_type` (`varchar`, `default: 'manual'`) - 分类方式: `'manual'`, `'ai'`, `'alphabetical'`
- `is_duplicate` (`boolean`, `default: false`) - 是否为重复书签
- `duplicate_url` (`text`, nullable) - 重复的URL地址
- `created_at` (`timestamptz`, `default: now()`) - 创建时间

### 3.3. 存储策略优化说明

#### 📁 **Cloudflare R2 文件存储结构 - 最简化版本**

```text
/bookmarks/{user_id}/
  ├── original.html         # 用户上传的原始书签文件
  └── result.html          # 整理完成的书签文件(用户下载)
```

**文件说明**:
- **original.html**: 用户上传的原始书签文件，用于AI分类处理
- **result.html**: 用户整理完成后的最终书签文件，可直接导入浏览器

**撤销机制**: 由前端控制，维护操作历史栈，提供撤销/重做/重置功能

#### 🔄 **数据流转流程**
1. **上传阶段**:
   - 新文件直接覆盖original.html (简化处理)
   - 元数据存入`user_bookmark_workspace`

2. **处理阶段**:
   - 从R2读取original.html
   - 解析后存入`bookmark_processing_cache`

3. **分类阶段**:
   - AI分类结果更新到缓存表

4. **确认阶段**:
   - 用户修正存入缓存表和AI学习示例
   - **前端维护操作历史**，支持撤销/重做

5. **导出阶段**:
   - 根据缓存表生成整理后的HTML
   - 保存为result.html到R2
   - 清空缓存表

6. **撤销功能** (前端实现):
   - **撤销(Undo)**: 回退到上一步操作
   - **重做(Redo)**: 前进到下一步操作
   - **重置(Reset)**: 回到刚上传original.html时的状态

#### 💰 **成本优化效果**
- **数据库存储**: 减少90%以上的存储需求
- **查询性能**: 大幅提升，只查询元数据
- **防滥用**: 每用户限制一个工作空间
- **存储成本**: R2成本远低于数据库存储

## 4. API 端点设计 (MVP) - 优化版本

API遵循RESTful风格，以资源为中心进行设计。所有API端点都需要用户认证（通过JWT）。

**核心变化**: 基于用户工作空间模式，每个用户只有一个活跃的整理工作空间。

| 端点路径 | HTTP方法 | 描述 | 请求体 (Body) | 成功响应 (200 OK) |
| :--- | :--- | :--- | :--- | :--- |
| `/api/workspace/upload` | `POST` | 用户上传HTML文件到工作空间，上传到R2并启动解析和AI分类任务。 | `FormData` (包含`.html`文件) | `{ "workspaceId": "uuid", "status": "processing" }` |
| `/api/workspace`| `GET` | 获取用户工作空间的完整数据，包括处理状态和书签缓存数据。 | (无) | `{ "workspace": {...}, "bookmarks": [...], "categories": [...] }` |
| `/api/workspace/bookmarks/batch`| `PATCH` | 批量更新书签分类。主要用于将多个书签移动到同一个新分类中。 | `{ "bookmarkIds": ["uuid1", ...], "categoryName": "Technology" }` | `{ "success": true, "count": 5 }` |
| `/api/workspace/bookmarks/:id` | `PATCH` | 更新单个书签的分类。用于用户手动修正分类，会更新AI学习示例。 | `{ "categoryName": "New Category" }` | `{ "updatedBookmark": {...} }` |
| `/api/categories` | `POST` | 创建新的用户分类模板。 | `{ "name": "New Category", "type": "manual", "color": "#ff0000" }` | `{ "newCategory": {...} }` |
| `/api/categories/:id`| `PATCH` | 更新分类模板的信息。 | `{ "name": "Updated Name", "color": "#00ff00" }` | `{ "updatedCategory": {...} }` |
| `/api/workspace/export` | `GET` | 生成整理好的HTML文件，保存到R2并返回下载链接。 | (无) | `{ "downloadUrl": "https://r2.../result.html" }` |
| `/api/workspace/reset` | `POST` | 重置工作空间到初始状态，清空缓存数据，重新从原始文件开始。 | (无) | `{ "success": true, "status": "reset" }` |

## 5. 核心算法与逻辑

### 5.1. 书签HTML递归解析逻辑 (后端) - 优化版本

1. **文件接收与存储**: 在 Next.js 的 API Route 中接收客户端上传的 `FormData`，直接上传到Cloudflare R2存储。
2. **从R2读取解析**: 从R2读取文件内容，加载到 Cheerio 实例中进行DOM操作。
3. **递归解析**: 解析HTML结构，提取书签数据存入临时缓存表。

    ```javascript
    // 后端API Route中的伪代码示例 - 优化版本
    async function processBookmarkFile(userId, fileBuffer) {
        // 1. 上传文件到R2
        const fileKey = `bookmarks/${userId}/original.html`;
        await uploadToR2(fileKey, fileBuffer);

        // 2. 解析HTML内容
        const $ = cheerio.load(fileBuffer.toString());
        const bookmarks = parseBookmarks($('body > dl').first(), []);

        // 3. 存入缓存表而非永久存储
        const workspace = await getUserWorkspace(userId);
        await clearProcessingCache(workspace.id); // 清空旧缓存

        for (const bookmark of bookmarks) {
            await insertToCache(workspace.id, {
                title: bookmark.title,
                url: bookmark.url,
                original_path: bookmark.original_path,
                status: 'unprocessed'
            });
        }

        // 4. 启动AI分类任务
        await startAIClassification(workspace.id);
    }

    function parseBookmarks(node, currentPath = []) {
        const bookmarks = [];
        node.children('dt').each((index, dtNode) => {
            const dt = $(dtNode);
            const link = dt.children('a').first();
            const folder = dt.children('h3').first();

            if (link.length > 0) {
                bookmarks.push({
                    title: link.text(),
                    url: link.attr('href'),
                    original_path: currentPath.join(' / ')
                });
            } else if (folder.length > 0) {
                const folderName = folder.text();
                const subDl = dt.next('dl').first();
                if (subDl.length > 0) {
                    const newPath = [...currentPath, folderName];
                    bookmarks.push(...parseBookmarks(subDl, newPath));
                }
            }
        });
        return bookmarks;
    }
    ```

### 5.2. AI 分类与“会话级学习”实现

*   **核心机制**: 通过在每次API请求中动态构建带有上下文示例的提示（Few-Shot Prompting）来实现。
*   **Prompt 模板**:

    ```text
    # Role: Bookmark Classification Expert

    # Task
    You are an expert at classifying browser bookmarks. Your goal is to assign the most accurate category to the given bookmark based on its title and URL. You must only use one of the provided categories.

    # Available Categories
    {{available_categories}}

    # Examples (Learn from these user corrections during this session)
    {{#each few_shot_examples}}
    - User Bookmark: "{{this.title}}" ({{this.url}}) -> Correct Category: {{this.category}}
    {{/each}}
    
    # New Bookmark to Classify
    - Title: "{{bookmark.title}}"
    - URL: "{{bookmark.url}}"

    # Output Format
    Return ONLY a JSON object with two keys: "category" and "confidence" (a float between 0 and 1 indicating your certainty). Do not add any other text, explanation or markdown formatting.
    ```
**后端处理流程** (优化版本):
1. **启动分类任务**: 当书签解析完成后，对缓存表中的所有书签发起AI分类任务。
2. **获取学习示例**: 从`user_bookmark_workspace`表中获取当前工作空间的`ai_learning_examples`数组。
3. **获取可用分类**: 从`user_categories`表中获取用户定义的所有分类模板。
4. **构建Prompt**: 使用学习示例和可用分类动态构建完整的Prompt。
5. **调用AI API**: 调用OpenAI API，将返回的JSON存入缓存表的`ai_suggestion`字段。
6. **实现学习**: 当用户修正分类时（触发 `PATCH /api/workspace/bookmarks/:id`），后端API会：
   - 更新缓存表中的`final_category`字段
   - 将修正案例追加到工作空间的`ai_learning_examples`数组中
   - 后续的AI分类会参考这些新的准确示例

**优化特点**:
- 基于工作空间的学习机制，每个用户独立学习
- 临时缓存存储，避免永久数据膨胀
- 学习示例持久化在工作空间中，支持跨会话学习


## 6. 部署与运维 (DevOps) - 优化版本

- **部署 (Deployment)**:
  - 项目主分支 (`main`) 与Vercel项目直接关联，自动触发构建和部署流程。

- **环境管理 (Environment Management)**:
  - **生产环境**: `main`分支。
  - **预览环境**: Pull Request自动生成独立预览URL。
  - **环境变量**: 敏感密钥存储在Vercel项目设置中，包括：
    - `SUPABASE_URL` 和 `SUPABASE_ANON_KEY`
    - `OPENAI_API_KEY`
    - **新增**: `CLOUDFLARE_R2_ACCESS_KEY_ID`, `CLOUDFLARE_R2_SECRET_ACCESS_KEY`, `CLOUDFLARE_R2_BUCKET_NAME`

- **持续集成/持续部署 (CI/CD)**:
  - Vercel平台提供开箱即用的CI/CD，无需复杂配置。

- **监控 (Monitoring)**:
  - **前端性能**: 使用 **Vercel Analytics** 监控访问量和Web Vitals。
  - **数据库**: 使用 **Supabase** 仪表盘监控数据库性能和API请求。
  - **对象存储**: 使用 **Cloudflare Analytics** 监控R2存储使用量和访问模式。

- **成本监控 (Cost Management)**:
  - **Vercel**: 监控函数执行时间和带宽使用
  - **Supabase**: 监控数据库存储和查询次数
  - **Cloudflare R2**: 监控存储容量和API调用次数（成本极低）
  - **OpenAI**: 监控API调用次数和token使用量

- **后台任务 (Background Tasks)**:
  - 使用 **Vercel Cron Jobs** 执行定时任务：
    - 清理过期的处理缓存数据
    - 定期健康检查书签链接（未来功能）
    - 清理R2中的临时文件

- **备份策略 (Backup Strategy)**:
  - **数据库**: Supabase自动备份
  - **R2文件**: 用户文件自动覆盖，无需额外备份
  - **用户数据**: 通过导出功能让用户自行备份