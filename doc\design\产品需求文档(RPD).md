# 产品需求文档 (PRD) - 智能书签管家 (Bookmark Tidy)
**版本: 1.0**
**文档状态: 初稿**
**创建日期: 2025年07月22日**

---

## 1. 文档修订历史

| 版本 | 日期 | 修订人 | 修订说明 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2025-07-22 | AI Assistant | 创建文档，包含产品概述、核心功能(MVP)、用户流程和非功能性需求。 |

---

## 2. 产品概述

### 2.1. 项目背景
知识工作者、开发者和学生等重度互联网用户，在日常工作和学习中会收藏大量的网页链接作为书签。随着时间推移，这些书签会变得杂乱无章，主要体现在：**分类结构混乱**、**命名不规范导致难以搜索**、**链接失效（死链）** 以及 **内容重复**。这极大地降低了书签作为个人知识库的价值和可用性。

### 2.2. 产品愿景
**“Bookmark Tidy”** 致力于成为一个优雅、智能的书签整理与管理工具。它通过自动化的解析、智能化的分类和人性化的交互，帮助用户在几分钟内将其成百上千的混乱书签，重构为一个结构清晰、命名规范、健康有效的全新知识库，并轻松导回浏览器或在云端进行管理。

### 2.3. 目标用户群体

1.  **重度知识工作者 (主要用户)**
    *   **画像**: 软件工程师、研究员、产品经理、在校学生等。
    *   **特征**: 每日接触大量信息，有强烈的知识收集和整理需求。浏览器书签通常超过500条，但缺乏有效管理。
    *   **核心痛点**: “信息收藏-整理”环节断裂，导致知识检索效率低下；手动整理耗时耗力，望而却步。

2.  **效率工具爱好者 (次要用户)**
    *   **画像**: 乐于尝试各类可提升生产力的工具的用户。
    *   **特征**: 对自动化、AI辅助工作流程有浓厚兴趣，愿意为好的体验付费。
    *   **核心痛点**: 寻求比浏览器自带功能更强大、更智能的书签管理方案。

## 3. 功能需求规格

### 3.1. V1.0 核心功能 (MVP)

#### 3.1.1. 用户账户系统 (Auth)
-   **F-AUTH-01: 基础注册与登录**
    -   **描述**: 用户可通过邮箱和密码创建账户并登录系统。
    -   **验收标准**: 用户可以使用有效的邮箱和密码成功创建账户并登录。密码在传输和存储过程中需加密。
-   **F-AUTH-02: 第三方OAuth登录**
    -   **描述**: 支持用户通过 Google账号一键登录或注册，简化流程。
    -   **验收标准**: 用户点击相应按钮后，能跳转至第三方授权页面，授权后自动登录或创建账户。
-   **F-AUTH-03: 密码重置**
    -   **描述**: 提供“忘记密码”功能，用户可通过邮箱验证来重置密码。
    -   **验收标准**: 用户输入注册邮箱后，能收到一封包含重置链接的邮件，点击链接可设置新密码。

#### 3.1.2. 书签导入与处理 (Import & Process)
-   **F-IMP-01: 浏览器书签文件导入**
    -   **描述**: 用户可以上传主流浏览器 (Chrome, Edge, Firefox) 导出的 `HTML` 格式书签文件。
    -   **验收标准**: 界面需有明确的上传区域（支持拖拽），并提供清晰的上传和解析进度提示。支持文件大小至少10MB。
-   **F-IMP-02: 书签数据解析**
    -   **描述**: 系统后端能精确解析HTML文件，提取每个书签的**原始标题**、**URL**和**原始文件目录结构**（即它在原书签文件中的文件夹路径）。
    -   **验收标准**: 能够正确处理深度嵌套的文件夹结构。能识别并忽略分隔符等非链接元素。

#### 3.1.3. 整理与分类核心 (Tidy & Classify)
-   **F-TIDY-01: 可交互的整理界面**
    -   **描述**: 以“分类目录树 (左侧) + 书签列表 (右侧)”的经典布局，展示所有书签。
    -   **验收标准**: 用户可以点击左侧目录树来筛选右侧列表显示的书签。界面响应迅速。
-   **F-TIDY-02: 手动整理功能**
    -   **描述**: 用户拥有完整的整理权限，包括：拖拽移动一个或多个书签到不同的分类；创建、重命名、删除分类；直接在列表中编辑书签的标题。
    -   **验收标准**: 所有手动操作都有即时视觉反馈，且支持撤销(Undo)。
-   **F-TIDY-03: AI 智能分类 (会话级学习)**
    -   **描述**: 对所有导入的书签，AI 自动为其建议一个分类。AI建议的分类和置信度会清晰地展示在每个书签旁边。用户可以**一键接受**或**修改**AI的建议。当用户修正分类后，AI在该次会话中，对后续相似书签的分类会参考用户的修正进行调整（Few-Shot Learning）。
    -   **验收标准**: 每个待处理书签都清晰标出AI建议。用户修改一个分类后，处理同类型书签时AI的建议有明显改善。
-   **F-TIDY-04: 收件箱/待办概念 (Inbox)**
    -   **描述**: 所有新导入的书签，以及AI分类置信度低于预设阈值（如70%）的书签，会默认进入一个名为“待处理”或“收件箱”的特殊列表中，引导用户优先审核。
    -   **验收标准**: 界面上有明确的计数器，告知用户还有多少书签需要确认，给用户明确的任务目标。
-   **F-TIDY-05: 字母分类功能 (Alphabetical Classification)**
    -   **描述**: 除了AI智能分类外，系统提供按书签名称首字母进行自动分类的功能。用户可以选择将书签按A-Z的字母顺序自动归类到对应的字母分类文件夹中（如"A类"、"B类"等），为用户提供另一种快速整理书签的方式。
    -   **验收标准**: 用户可以一键启用字母分类模式，系统自动根据书签标题的首字母创建相应分类并移动书签。支持中文书签按拼音首字母分类。用户可以在AI分类和字母分类之间切换选择。

#### 3.1.4. 书签导出 (Export)
-   **F-EXP-01: 生成新的HTML书签文件**
    -   **描述**: 用户整理完毕后，可以一键生成一个与浏览器导入格式完全兼容的新HTML文件。
    -   **验收标准**: 生成的文件能被Chrome/Edge等浏览器正确识别并导入，其目录结构与用户在Web界面整理的结果完全一致。

### 3.2. 未来版本规划 (V1.1+)

-   **F-ADV-01: 自然语言指令分类**: 用户可在输入框输入指令，如“把所有包含`github.com`的链接都放到`开发`分类下”。
-   **F-ADV-02: 用户自定义规则**: 用户可设定持久化的规则，如 `IF 域名="medium.com" THEN 分类="技术文章"`。
-   **F-HEA-01: 书签健康检查**: 异步扫描用户的书签，识别死链 (404)、无权限链接 (403) 和重复链接，并在UI上标记，提供一键清理。
-   **F-CLD-01: 云端同步与存储**: 用户可将整理好的书签库保存在云端，实现跨设备访问和管理，摆脱对文件的依赖。
-   **F-ECO-01: 浏览器扩展**: 开发浏览器插件，实现一键添加新书签至云端并自动分类。

## 4. 用户流程与界面设计

### 4.1. 核心用户旅程图

| 阶段 | 用户行为 | 系统交互/界面 | 情绪/感受 |
| :--- | :--- | :--- | :--- |
| **1. 发现与登录** | 发现网站，通过Google账号登录。 | 简洁的落地页，清晰的登录按钮。 | 好奇，期待 |
| **2. 导入书签** | 从浏览器导出书签文件，拖拽上传至网站。 | 仪表盘界面，醒目的“上传文件”区域，显示上传和解析进度条。 | 轻松，便捷 |
| **3. 智能分类与审核** | 查看AI初步分类的结果，进入“待处理”列表。 | 左右布局的整理界面，AI建议清晰可见，节省大量时间。 | 惊艳，高效 |
| **4. 微调与确认** | 拖拽几个AI分错的书签到正确分类，批量接受其他建议。 | 拖拽操作流畅，即时反馈。AI后续建议更精准，感觉被理解。 | 有掌控感，智能 |
| **5. 导出与完成** | 点击“导出”按钮，下载整理好的文件。将新文件导回浏览器。 | 下载提示，成功信息，附带简单的导回浏览器教程。 | 满足，成就感 |

### 4.2. 核心交互流程图 (User Flow)
<!-- 
该图表使用Mermaid.js语法。
请在支持的Markdown渲染器中查看（如Typora, VSCode with Mermaid extension）。
-->
```mermaid
graph TD
    A(Start) --> B{已登录?};
    B -- No --> C[登录/注册页];
    C --> D[仪表盘 Dashboard];
    B -- Yes --> D;
    D -- 上传书签文件 --> E[后台处理: 解析 & AI分类];
    E --> F[整理与审核界面];
    F --> G{手动调整};
    G -- 拖拽书签 --> F;
    G -- 修改分类 --> F;
    G -- 确认所有分类 --> H[导出页面];
    H --> I{选择导出格式};
    I -- 生成HTML文件 --> J[下载文件];
    J --> K(End);
```

## 5. 非功能性需求
    - 性能: 书签文件解析和初步分类应在1分钟内完成（针对1000条书签）。所有界面交互响应时间低于200ms。
    - 安全性: 用户数据（尤其是书签内容）在传输和静止时都必须加密。用户之间的数据必须严格隔离。
    - 可用性: 产品必须易于理解，核心流程无需教程即可完成。需提供必要的帮助文档和引导提示。
    - 兼容性: Web界面需在最新版的Chrome, Edge, Firefox, Safari浏览器上表现良好。导出的文件必须与这些主流浏览器兼容。
    - 可扩展性: 系统架构应能支持未来用户量和功能模块的平滑增长，特别是在引入云同步功能后。