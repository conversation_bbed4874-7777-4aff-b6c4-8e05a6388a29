# 智能书签管家 (Bookmark Tidy) - 项目执行计划
**文档版本: 1.0**
**日期: 2025-07-22**

## 1. 文档概述

本执行计划旨在为“智能书签管家”项目（Bookmark Tidy）的V1.0（MVP）开发，提供一个清晰、分阶段、可操作的路线图。它详细说明了从项目启动到最终部署的各个阶段、任务、时间表、资源需求和潜在风险，以确保项目能够高效、有序地推进。

**核心目标 (MVP):** 开发一个功能性的Web应用，用户可以上传浏览器书签，通过手动和AI辅助的方式进行整理，并最终导出一个全新的、干净的HTML书签文件。

## 2. 项目资源与团队

*   **团队构成**: 独立开发（1人角色，负责全栈开发、产品与运维）。
*   **核心技术栈** (优化版本):
    *   **全栈框架**: Next.js
    *   **后端即服务**: Supabase (Database, Auth)
    *   **对象存储**: Cloudflare R2 (书签文件存储)
    *   **部署平台**: Vercel
    *   **AI 服务**: OpenAI API
*   **开发工具**:
    *   **代码编辑器**: VS Code
    *   **版本控制**: Git / GitHub
    *   **API 测试**: Postman / Bruno
    *   **设计/线框图**: Figma / Excalidraw (可选)

## 3. 总体开发时间线 (Gantt Chart)

这是一个预估的时间线，假设开发者有充足的投入时间。总计 **约4-6周** 完成MVP。

```mermaid
gantt
    title Bookmark Tidy - MVP Development Timeline
    dateFormat  YYYY-MM-DD
    section Phase 0: Foundation & Setup
    Project Setup        :done, 2025-07-23, 2d
    section Phase 1: Core Backend
    Database & Auth      :done, 2025-07-25, 3d
    Develop Core APIs    :active, 2025-07-28, 4d
    section Phase 2: Core Frontend
    Build UI Layout      :2025-08-01, 4d
    Develop UI Components:2025-08-05, 7d
    Integrate Frontend & API:2025-08-12, 5d
    section Phase 3: AI Integration
    Develop AI Service   :2025-08-17, 3d
    Integrate AI in UI   :2025-08-20, 2d
    section Phase 4: Polish & Deploy
    Testing & Bug Fixing :2025-08-22, 4d
    Final Deployment     :2025-08-26, 2d
```

## 4. 分阶段执行计划 (Task Breakdown)

### **Phase 0: 基础与环境设置 (预计: 2天)**
*   **目标**: 准备好所有开发所需的环境和工具。
*   **任务清单** (优化版本):
    - [ ] **T0.1**: 创建一个新的 GitHub 私有仓库。
    - [ ] **T0.2**: 在 Supabase 上创建新项目，获取 API Keys 和数据库URL。
    - [ ] **T0.3**: 在 Cloudflare 上创建 R2 存储桶，获取访问密钥。
    - [ ] **T0.4**: 使用 `create-next-app` 初始化 Next.js 项目，并集成 Tailwind CSS。
    - [ ] **T0.5**: 将项目首次推送到 GitHub 仓库。
    - [ ] **T0.6**: 创建 `.env.local` 文件，配置环境变量：
        - Supabase (Database, Auth)
        - Cloudflare R2 (存储)
        - OpenAI API (AI服务)
    - [ ] **T0.7**: 将项目与 Vercel 平台连接，完成首次测试部署。
*   **交付成果**: 一个可以成功部署的、空白的Next.js项目，并已连接好版本控制和托管平台。

### **Phase 1: 核心后端开发 (预计: 7天)**
*   **目标**: 构建支撑核心功能的数据库结构和API接口。
*   **任务清单** (优化版本):
    - [ ] **T1.1**: **数据库建模**: 在 Supabase SQL 编辑器中，根据优化后的技术文档创建表结构：
        - `profiles` (用户信息)
        - `user_bookmark_workspace` (用户工作空间)
        - `user_categories` (分类模板)
        - `bookmark_processing_cache` (临时处理缓存)
        - 配置相应的RLS策略
    - [ ] **T1.2**: **用户认证**: 使用 Supabase Auth Helpers for Next.js 实现用户注册、登录、登出和第三方登录（优先实现Google）。
    - [ ] **T1.3**: **R2存储集成**: 集成 Cloudflare R2 SDK，实现文件上传、下载和删除功能。
    - [ ] **T1.4**: **文件上传API**: 创建 `/api/workspace/upload` 端点，实现接收HTML文件并上传到R2存储。
    - [ ] **T1.5**: **HTML解析服务**: 开发核心的 `cheerio.js` 解析逻辑，从R2读取文件，解析后存入临时缓存表。
    - [ ] **T1.6**: **工作空间API**: 开发用户工作空间的CRUD操作，包括获取状态、重置等。
    - [ ] **T1.7**: **分类管理API**: 开发用户分类模板的创建、读取、更新、删除功能。
    - [ ] **T1.8**: **书签处理API**: 开发书签缓存数据的读取和更新功能。
*   **交付成果**: 一套可通过API测试工具(Postman)完整调用的、功能齐全的后端服务。

### **Phase 2: 核心前端开发 (预计: 10-12天)**
*   **目标**: 构建用户能与之交互的核心界面。
*   **任务清单**:
    - [ ] **T2.1**: **页面骨架**: 搭建应用的基础布局（Layout），包括导航栏、侧边栏和主内容区。
    - [ ] **T2.2**: **组件开发**: 使用`shadcn/ui`创建可复用的UI组件：
        - `BookmarkCard`: 显示单个书签信息和操作。
        - `CategoryTree`: 可折叠的分类目录树。
        - `UploadModal`: 上传文件的模态框。
        - `UserAuthForm`: 用户登录/注册表单。
    - [ ] **T2.3**: **页面路由**: 创建主要页面：登录页、仪表盘/整理页。
    - [ ] **T2.4**: **状态管理与数据获取**: 集成 `SWR` 或 `TanStack Query` 来管理与后端API的交互（获取、缓存、更新数据）。
    - [ ] **T2.5**: **功能连接 - 用户流**: 将用户认证流程的前端界面与后端API打通。
    - [ ] **T2.6**: **功能连接 - 整理交互**: 实现核心的拖拽排序、书签移动、分类创建等前端交互逻辑，并调用相应的API更新后端数据。
*   **交付成果**: 一个功能性的前端界面。用户可以登录，上传书签，看到解析后的数据，并进行手动整理。

### **Phase 3: AI智能集成 (预计: 5天)**
*   **目标**: 引入AI能力，实现智能分类的核心亮点功能。
*   **任务清单**:
    - [ ] **T3.1**: **Prompt设计与测试**: 在 OpenAI Playground 中反复调试和优化用于分类的Prompt模板，确保其高效、稳定，并能返回JSON格式。
    - [ ] **T3.2**: **AI分类后端服务**: 创建一个新的API端点 (e.g., `/api/ai/classify-bookmark`)，该端点接收书签信息，构建Prompt，调用OpenAI API，并将结果返回。
    - [ ] **T3.3**: **“会话级学习”逻辑**: 在后端实现将用户修正的分类案例（Few-shot examples）存入`user_sessions`表，并在下次调用AI时动态加载进Prompt中。
    - [ ] **T3.4**: **前端集成**: 在前端调用AI分类服务，并将返回的“AI建议”和“置信度”显示在`BookmarkCard`组件中。
    - [ ] **T3.5**: **前端交互**: 实现“接受”和“修改”AI建议的按钮功能。点击修改后，调用后端API更新分类并同步更新`few_shot_examples`。
*   **交付成果**: MVP的核心亮点功能完成。用户能体验到AI辅助分类，并感觉AI能“听取”其修改意见。

### **Phase 4: 测试、优化与部署 (预计: 4-6天)**
*   **目标**: 确保产品质量，修复Bug，并正式上线MVP。
*   **任务清单**:
    - [ ] **T4.1**: **端到端测试**: 完整地模拟用户走通所有流程：注册->登录->上传->整理->修改AI建议->导出。
    - [ ] **T4.2**: **跨浏览器测试**: 在最新版的Chrome, Firefox, Safari中测试核心功能。
    - [ ] **T4.3**: **响应式设计优化**: 确保应用在桌面和主流移动设备上都有良好的显示效果。
    - [ ] **T4.4**: **性能审视**: 检查大数据量（如5000条书签）下的上传解析速度，进行必要的优化。
    - [ ] **T4.5**: **购买域名**: 购买产品域名。
    - [ ] **T4.6**: **正式部署**: 将最终代码合并到主分支，触发Vercel的生产环境部署，并将域名指向Vercel项目。
*   **交付成果**: 一个公开可访问的、稳定运行的V1.0产品。

### **Phase 5: 发布后工作 (持续)**
*   **目标**: 收集用户反馈，规划未来迭代。
*   **任务清单**:
    - [ ] **T5.1**: 监控Vercel和Supabase的后台日志，观察有无异常。
    - [ ] **T5.2**: 创建一个简单的反馈渠道（如Google Form或嵌入式工具）。
    - [ ] **T5.3**: 根据用户反馈和自己的观察，整理Bug列表和新功能需求池。
    - [ ] **T5.4**: 基于PRD中的V1.1规划，开始下一个迭代周期的设计和开发。

## 5. 风险管理

| 风险类别 | 风险描述 | 可能性 | 影响程度 | 缓解措施 |
| :--- | :--- | :--- | :--- | :--- |
| **技术风险** | HTML文件格式多样性远超预期，解析逻辑复杂。 | 中 | 高 | 设定明确的MVP支持格式(Chrome/Edge)；预留额外时间进行兼容性调试；在产品中注明当前支持的浏览器类型。 |
| **成本风险** | OpenAI API调用费用超出预期。 | 中 | 中 | 初期要求用户填写自己的API Key；优化Prompt以减少Token消耗；为高频用户提供缓存机制；选择更便宜的模型（如GPT-3.5-Turbo）。|
| **项目管理** | 范围蔓延，在MVP阶段试图加入过多功能。 | 高 | 高 | 严格遵守本执行计划的MVP功能列表；将所有新想法记录在“未来版本”的待办事项中，而非当前开发周期。 |
| **个人风险** | 作为独立开发者，可能因其他事务中断或产生倦怠。 | 高 | 高 | 制定实际的每日/每周工作计划并遵守；保持工作与生活的平衡；为每个阶段设定小目标并庆祝完成，获得持续的正反馈。 |

## 6. 预算估算 (优化版本)
- **托管费用 (Vercel)**: $0 (Hobby Plan)
- **数据库费用 (Supabase)**: $0 (Free Plan)
- **对象存储费用 (Cloudflare R2)**: ~$0-2/月 (前10GB免费，$0.015/GB/月)
- **AI调用费用 (OpenAI)**: ~$0-5/月 (可依赖免费额度或要求用户提供Key)
- **域名费用**: ~$15 / 年
- **总计MVP开发和运营成本**: **约 $15-50/年**

**成本优化效果**:
- 通过R2存储替代数据库存储，大幅降低存储成本
- 每用户限制一个工作空间，有效控制存储增长
- R2无出站流量费用，降低数据传输成本